"""
增量采集助手测试用例

测试IncrementalCollectionHelper的所有功能，包括增量范围计算、
历史状态查询、交易日历集成等功能。

Author: AQUA Team
Date: 2025-01-26
Version: 1.0
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data_import.incremental_collection_helper import IncrementalCollectionHelper


class TestIncrementalCollectionHelper(unittest.TestCase):
    """增量采集助手测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.helper = IncrementalCollectionHelper()
        
        # Mock依赖组件
        self.mock_history_manager = Mock()
        self.mock_trading_calendar = Mock()
        self.mock_config_loader = Mo<PERSON>()
        
        self.helper.history_manager = self.mock_history_manager
        self.helper.trading_calendar = self.mock_trading_calendar
        self.helper.config_loader = self.mock_config_loader
    
    def test_calculate_incremental_range_with_history(self):
        """测试有历史记录时的增量范围计算"""
        # 模拟历史记录查询
        self.mock_history_manager.get_last_import_date.return_value = '2024-12-01'
        
        # 模拟交易日历查询
        self.mock_trading_calendar.get_next_trading_date.return_value = '2024-12-02'
        
        # 执行测试
        result = self.helper.calculate_incremental_range(
            symbols=['000001.SZ'],
            data_type='stocks',
            freq='daily',
            source='TUSHARE'
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['start_date'], '2024-12-02')
        self.assertIn('end_date', result)
        
        # 验证调用
        self.mock_history_manager.get_last_import_date.assert_called()
        self.mock_trading_calendar.get_next_trading_date.assert_called_with(
            '2024-12-01', 'STOCK', 'SZSE'
        )
    
    def test_calculate_incremental_range_no_history(self):
        """测试无历史记录时使用默认配置"""
        # 模拟无历史记录
        self.mock_history_manager.get_last_import_date.return_value = None
        
        # 模拟配置查询
        self.mock_config_loader.get.return_value = 30
        
        # 执行测试
        result = self.helper.calculate_incremental_range(
            symbols=['000001.SZ'],
            data_type='stocks',
            freq='daily'
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn('start_date', result)
        self.assertIn('end_date', result)
        
        # 验证配置查询
        self.mock_config_loader.get.assert_called_with(
            'data_collection.default_days.stocks', 30
        )
    
    def test_calculate_incremental_range_no_new_data(self):
        """测试无新数据时返回None"""
        # 模拟历史记录查询
        self.mock_history_manager.get_last_import_date.return_value = '2024-12-01'
        
        # 模拟交易日历查询返回None（无下一交易日）
        self.mock_trading_calendar.get_next_trading_date.return_value = None
        
        # 执行测试
        result = self.helper.calculate_incremental_range(
            symbols=['000001.SZ'],
            data_type='stocks'
        )
        
        # 验证结果
        self.assertIsNone(result)
    
    def test_calculate_incremental_range_future_date(self):
        """测试下一交易日超过今天时返回None"""
        # 模拟历史记录查询
        self.mock_history_manager.get_last_import_date.return_value = '2024-12-01'
        
        # 模拟交易日历查询返回未来日期
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        self.mock_trading_calendar.get_next_trading_date.return_value = tomorrow
        
        # 执行测试
        result = self.helper.calculate_incremental_range(
            symbols=['000001.SZ'],
            data_type='stocks'
        )
        
        # 验证结果
        self.assertIsNone(result)
    
    def test_get_exchange_code_stocks(self):
        """测试股票交易所代码获取"""
        # 测试上交所
        result = self.helper._get_exchange_code('600000.SH', 'stocks')
        self.assertEqual(result, 'SSE')
        
        # 测试深交所
        result = self.helper._get_exchange_code('000001.SZ', 'stocks')
        self.assertEqual(result, 'SZSE')
        
        # 测试默认情况
        result = self.helper._get_exchange_code('000001', 'stocks')
        self.assertEqual(result, 'SSE')
    
    def test_get_exchange_code_futures(self):
        """测试期货交易所代码获取"""
        # 测试上期所
        result = self.helper._get_exchange_code('RB2501', 'futures')
        self.assertEqual(result, 'SHFE')
        
        result = self.helper._get_exchange_code('CU2501', 'futures')
        self.assertEqual(result, 'SHFE')
        
        # 测试中金所
        result = self.helper._get_exchange_code('IF2501', 'futures')
        self.assertEqual(result, 'CFFEX')
        
        # 测试大商所
        result = self.helper._get_exchange_code('A2501', 'futures')
        self.assertEqual(result, 'DCE')
        
        # 测试郑商所
        result = self.helper._get_exchange_code('CF2501', 'futures')
        self.assertEqual(result, 'CZCE')
        
        # 测试默认情况
        result = self.helper._get_exchange_code('UNKNOWN2501', 'futures')
        self.assertEqual(result, 'SHFE')
    
    def test_get_default_range_from_settings_different_types(self):
        """测试不同数据类型的默认范围配置"""
        # 测试股票数据
        self.mock_config_loader.get.return_value = 30
        result = self.helper._get_default_range_from_settings('stocks', 'daily')
        
        self.assertIn('start_date', result)
        self.assertIn('end_date', result)
        self.mock_config_loader.get.assert_called_with(
            'data_collection.default_days.stocks', 30
        )
        
        # 测试期货数据
        self.mock_config_loader.get.return_value = 60
        result = self.helper._get_default_range_from_settings('futures', 'daily')
        
        self.mock_config_loader.get.assert_called_with(
            'data_collection.default_days.futures', 60
        )
        
        # 测试分钟数据
        self.mock_config_loader.get.return_value = 7
        result = self.helper._get_default_range_from_settings('stocks', '1min')
        
        self.mock_config_loader.get.assert_called_with(
            'data_collection.default_days.minutes', 7
        )
    
    def test_get_default_range_from_settings_config_error(self):
        """测试配置获取失败时的处理"""
        # 模拟配置获取失败
        self.mock_config_loader.get.side_effect = Exception("Config error")
        
        # 执行测试
        result = self.helper._get_default_range_from_settings('stocks', 'daily')
        
        # 验证使用硬编码默认值
        self.assertIn('start_date', result)
        self.assertIn('end_date', result)
        
        # 验证时间范围（应该是30天）
        start_date = datetime.strptime(result['start_date'], '%Y-%m-%d')
        end_date = datetime.strptime(result['end_date'], '%Y-%m-%d')
        delta = (end_date - start_date).days
        self.assertEqual(delta, 30)
    
    def test_should_use_incremental_with_user_range(self):
        """测试用户指定时间范围时不建议增量采集"""
        result = self.helper.should_use_incremental(
            symbols=['000001.SZ'],
            data_type='stocks',
            start_date='2024-11-01',
            end_date='2024-12-01'
        )
        
        self.assertFalse(result)
    
    def test_should_use_incremental_with_history(self):
        """测试有历史记录时建议增量采集"""
        # 模拟有历史记录
        self.mock_history_manager.get_last_import_date.return_value = '2024-12-01'
        
        result = self.helper.should_use_incremental(
            symbols=['000001.SZ'],
            data_type='stocks'
        )
        
        self.assertTrue(result)
    
    def test_should_use_incremental_no_history(self):
        """测试无历史记录时不建议增量采集"""
        # 模拟无历史记录
        self.mock_history_manager.get_last_import_date.return_value = None
        
        result = self.helper.should_use_incremental(
            symbols=['000001.SZ'],
            data_type='stocks'
        )
        
        self.assertFalse(result)
    
    def test_get_last_collection_dates_multiple_symbols(self):
        """测试多个标的的历史日期查询"""
        # 模拟不同标的的历史记录
        def mock_get_last_date(symbol, **kwargs):
            if symbol == '000001.SZ':
                return '2024-12-01'
            elif symbol == '600000.SH':
                return '2024-11-30'
            else:
                return None
        
        self.mock_history_manager.get_last_import_date.side_effect = mock_get_last_date
        
        # 执行测试
        result = self.helper._get_last_collection_dates(
            symbols=['000001.SZ', '600000.SH', '000002.SZ'],
            data_type='stocks',
            freq='daily',
            source='TUSHARE'
        )
        
        # 验证结果
        expected = {
            '000001.SZ': '2024-12-01',
            '600000.SH': '2024-11-30',
            '000002.SZ': None
        }
        self.assertEqual(result, expected)
    
    def test_calculate_range_from_history_multiple_dates(self):
        """测试基于多个历史日期计算增量范围"""
        # 模拟历史日期
        last_dates = {
            '000001.SZ': '2024-12-01',
            '600000.SH': '2024-11-30',  # 更早的日期
            '000002.SZ': '2024-12-02'
        }
        
        # 模拟交易日历查询
        self.mock_trading_calendar.get_next_trading_date.return_value = '2024-12-01'  # 基于最早日期
        
        # 执行测试
        result = self.helper._calculate_range_from_history(
            last_dates, ['000001.SZ', '600000.SH', '000002.SZ'], 'stocks', 'daily'
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['start_date'], '2024-12-01')
        
        # 验证使用最早的日期作为基准
        self.mock_trading_calendar.get_next_trading_date.assert_called_with(
            '2024-11-30', 'STOCK', 'SZSE'  # 使用最早的日期
        )
    
    def test_lazy_initialization(self):
        """测试延迟初始化功能"""
        # 创建新实例
        new_helper = IncrementalCollectionHelper()
        
        # 验证初始状态
        self.assertIsNone(new_helper.history_manager)
        self.assertIsNone(new_helper.trading_calendar)
        self.assertIsNone(new_helper.config_loader)
        
        # 模拟成功导入
        with patch('src.data_import.incremental_collection_helper.ImportHistoryManager') as mock_history:
            with patch('src.data_import.incremental_collection_helper.TradingCalendarManager') as mock_calendar:
                with patch('src.data_import.incremental_collection_helper.ConfigLoader') as mock_config:
                    
                    # 触发延迟初始化
                    history_manager = new_helper._get_history_manager()
                    trading_calendar = new_helper._get_trading_calendar()
                    config_loader = new_helper._get_config_loader()
                    
                    # 验证初始化
                    self.assertIsNotNone(history_manager)
                    self.assertIsNotNone(trading_calendar)
                    self.assertIsNotNone(config_loader)
                    
                    # 验证类被调用
                    mock_history.assert_called_once()
                    mock_calendar.assert_called_once()
                    mock_config.assert_called_once()


class TestIncrementalCollectionHelperIntegration(unittest.TestCase):
    """增量采集助手集成测试类"""
    
    def setUp(self):
        """集成测试前准备"""
        self.helper = IncrementalCollectionHelper()
    
    def test_exchange_code_mapping_comprehensive(self):
        """测试交易所代码映射的全面性"""
        # 测试主要期货品种
        test_cases = [
            # 上期所
            ('RB2501', 'SHFE'), ('CU2501', 'SHFE'), ('AL2501', 'SHFE'),
            ('AU2501', 'SHFE'), ('AG2501', 'SHFE'),
            
            # 中金所
            ('IF2501', 'CFFEX'), ('IC2501', 'CFFEX'), ('IH2501', 'CFFEX'),
            
            # 大商所
            ('A2501', 'DCE'), ('M2501', 'DCE'), ('Y2501', 'DCE'),
            
            # 郑商所
            ('CF2501', 'CZCE'), ('MA2501', 'CZCE'), ('SR2501', 'CZCE')
        ]
        
        for symbol, expected_exchange in test_cases:
            result = self.helper._get_exchange_code(symbol, 'futures')
            self.assertEqual(result, expected_exchange, 
                           f"Symbol {symbol} should map to {expected_exchange}")
    
    def test_date_range_calculation(self):
        """测试日期范围计算的准确性"""
        # 测试默认范围计算
        with patch.object(self.helper, '_get_config_loader') as mock_get_config:
            mock_config = Mock()
            mock_config.get.return_value = 30
            mock_get_config.return_value = mock_config
            
            result = self.helper._get_default_range_from_settings('stocks', 'daily')
            
            # 验证日期格式
            self.assertRegex(result['start_date'], r'\d{4}-\d{2}-\d{2}')
            self.assertRegex(result['end_date'], r'\d{4}-\d{2}-\d{2}')
            
            # 验证日期范围
            start_date = datetime.strptime(result['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(result['end_date'], '%Y-%m-%d')
            
            self.assertLessEqual(start_date, end_date)
            self.assertEqual((end_date - start_date).days, 30)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
