#!/usr/bin/env python3
"""
TaskQueueEngine 单元测试
基于TDD原则，验证项目任务队列引擎功能

测试覆盖:
1. 任务提交和执行
2. 优先级队列排序
3. 项目管理特定功能
4. 并发安全性
5. 错误处理
"""

import pytest
import time
import threading
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# 导入被测试的模块
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from core.task_queue_engine import (
    TaskQueueEngine, 
    TaskPriority, 
    TaskStatus,
    QueueTask
)


class TestTaskQueueEngine:
    """TaskQueueEngine单元测试类"""
    
    @pytest.fixture
    def task_engine_config(self):
        """测试配置夹具"""
        return {
            "max_queue_size": 100,
            "worker_count": 2,
            "max_retry_count": 3,
            "task_timeout": 30,
            "enable_monitoring": True
        }
    
    @pytest.fixture
    def task_engine(self, task_engine_config):
        """TaskQueueEngine测试夹具"""
        engine = TaskQueueEngine(task_engine_config)
        engine._test_mode = True  # 启用测试模式
        yield engine
        # 清理
        engine.shutdown()
    
    def test_task_engine_initialization(self, task_engine_config):
        """测试TaskQueueEngine初始化"""
        # Act
        engine = TaskQueueEngine(task_engine_config)
        
        # Assert
        assert engine.max_queue_size == 100
        assert engine.worker_count == 2
        assert engine.max_retry_count == 3
        assert engine.task_timeout == 30
        assert engine.enable_monitoring is True
        assert engine.get_task_count() == 0
        
        # 清理
        engine.shutdown()
    
    def test_submit_epic_task_success(self, task_engine):
        """测试Epic任务提交成功"""
        # Arrange
        epic_data = {
            "title": "用户认证系统",
            "description": "实现用户登录注册功能",
            "priority": "high",
            "estimated_hours": 40
        }
        
        # Act
        task_id = task_engine.submit_epic_task(epic_data)
        
        # Assert
        assert task_id is not None
        assert len(task_id) == 36  # UUID长度
        assert task_engine.get_task_count() == 1
        
        # 验证任务详情
        task = task_engine.get_task(task_id)
        assert task is not None
        assert task.task_type == "epic_creation"
        assert task.priority == TaskPriority.HIGH
        assert task.payload["title"] == "用户认证系统"
    
    def test_submit_feature_task_success(self, task_engine):
        """测试Feature任务提交成功"""
        # Arrange
        feature_data = {
            "title": "用户登录",
            "description": "实现用户登录功能",
            "epic_id": "epic-123",
            "estimated_hours": 8
        }
        
        # Act
        task_id = task_engine.submit_feature_task(feature_data)
        
        # Assert
        assert task_id is not None
        assert task_engine.get_task_count() == 1
        
        # 验证任务详情
        task = task_engine.get_task(task_id)
        assert task.task_type == "feature_creation"
        assert task.priority == TaskPriority.MEDIUM
        assert task.payload["epic_id"] == "epic-123"
    
    def test_submit_task_task_success(self, task_engine):
        """测试Task任务提交成功"""
        # Arrange
        task_data = {
            "title": "设计登录API",
            "description": "设计用户登录API接口",
            "feature_id": "feature-456",
            "assignee": "developer",
            "estimated_hours": 2
        }
        
        # Act
        task_id = task_engine.submit_task_task(task_data)
        
        # Assert
        assert task_id is not None
        assert task_engine.get_task_count() == 1
        
        # 验证任务详情
        task = task_engine.get_task(task_id)
        assert task.task_type == "task_creation"
        assert task.priority == TaskPriority.LOW
        assert task.payload["feature_id"] == "feature-456"
    
    def test_priority_queue_ordering(self, task_engine):
        """测试优先级队列排序"""
        # Arrange - 提交不同优先级的任务
        low_task_id = task_engine.submit_task_task({"title": "低优先级任务"})
        critical_task_id = task_engine.submit_epic_task({"title": "紧急Epic"}, TaskPriority.CRITICAL)
        medium_task_id = task_engine.submit_feature_task({"title": "中等Feature"})
        
        # Act - 获取下一个任务
        next_task = task_engine.get_next_task()
        
        # Assert - 应该返回最高优先级的任务
        assert next_task is not None
        assert next_task.task_id == critical_task_id
        assert next_task.priority == TaskPriority.CRITICAL
    
    def test_queue_capacity_limit(self, task_engine):
        """测试队列容量限制"""
        # Arrange
        task_engine.max_queue_size = 2
        
        # Act & Assert
        task_engine.submit_epic_task({"title": "Epic 1"})
        task_engine.submit_feature_task({"title": "Feature 1"})
        
        # 第三个任务应该抛出异常
        with pytest.raises(RuntimeError, match="队列已满"):
            task_engine.submit_task_task({"title": "Task 1"})
    
    def test_task_execution_success(self, task_engine):
        """测试任务执行成功"""
        # Arrange
        executed_tasks = []
        
        def mock_processor(task_type, payload):
            executed_tasks.append((task_type, payload))
            return {"status": "success", "result": "Epic created"}
        
        task_engine.register_processor("epic_creation", mock_processor)
        task_id = task_engine.submit_epic_task({"title": "Test Epic"})
        
        # Act
        task_engine.start_workers()
        time.sleep(0.1)  # 等待任务执行
        
        # Assert
        assert len(executed_tasks) == 1
        assert executed_tasks[0][0] == "epic_creation"
        assert executed_tasks[0][1]["title"] == "Test Epic"
        
        # 验证任务状态
        task = task_engine.get_task(task_id)
        assert task.status == TaskStatus.COMPLETED
    
    def test_concurrent_task_submission(self, task_engine):
        """测试并发任务提交"""
        # Arrange
        task_ids = []
        
        def submit_tasks():
            for i in range(10):
                task_id = task_engine.submit_epic_task({"title": f"Epic {i}"})
                task_ids.append(task_id)
        
        # Act - 并发提交任务
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=submit_tasks)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Assert
        assert len(task_ids) == 30  # 3个线程 × 10个任务
        assert task_engine.get_task_count() == 30
        assert len(set(task_ids)) == 30  # 所有任务ID应该唯一
    
    def test_task_cancellation(self, task_engine):
        """测试任务取消"""
        # Arrange
        task_id = task_engine.submit_epic_task({"title": "Cancellable Epic"})
        
        # Act
        result = task_engine.cancel_task(task_id)
        
        # Assert
        assert result is True
        task = task_engine.get_task(task_id)
        assert task.status == TaskStatus.CANCELLED
    
    def test_get_project_statistics(self, task_engine):
        """测试项目统计功能"""
        # Arrange
        task_engine.submit_epic_task({"title": "Epic 1"})
        task_engine.submit_feature_task({"title": "Feature 1"})
        task_engine.submit_feature_task({"title": "Feature 2"})
        task_engine.submit_task_task({"title": "Task 1"})
        
        # Act
        stats = task_engine.get_project_statistics()
        
        # Assert
        assert stats["total_tasks"] == 4
        assert stats["epic_count"] == 1
        assert stats["feature_count"] == 2
        assert stats["task_count"] == 1
        assert stats["pending_count"] == 4
        assert stats["completed_count"] == 0
    
    def test_invalid_task_data(self, task_engine):
        """测试无效任务数据处理"""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="任务标题不能为空"):
            task_engine.submit_epic_task({"description": "无标题Epic"})


class TestTaskQueueEngineIntegration:
    """TaskQueueEngine集成测试"""
    
    def test_complete_project_workflow(self):
        """测试完整项目工作流"""
        # Arrange
        config = {
            "max_queue_size": 50,
            "worker_count": 1,
            "max_retry_count": 2
        }
        engine = TaskQueueEngine(config)
        
        # 注册处理器
        results = []
        
        def epic_processor(task_type, payload):
            results.append(f"Epic: {payload['title']}")
            return {"status": "success", "epic_id": "epic-001"}
        
        def feature_processor(task_type, payload):
            results.append(f"Feature: {payload['title']}")
            return {"status": "success", "feature_id": "feature-001"}
        
        def task_processor(task_type, payload):
            results.append(f"Task: {payload['title']}")
            return {"status": "success", "task_id": "task-001"}
        
        engine.register_processor("epic_creation", epic_processor)
        engine.register_processor("feature_creation", feature_processor)
        engine.register_processor("task_creation", task_processor)
        
        try:
            # Act - 模拟完整项目创建流程
            epic_id = engine.submit_epic_task({"title": "用户认证系统"})
            feature_id = engine.submit_feature_task({"title": "用户登录", "epic_id": epic_id})
            task_id = engine.submit_task_task({"title": "设计登录API", "feature_id": feature_id})
            
            # 启动处理
            engine.start_workers()
            time.sleep(0.2)  # 等待处理完成
            
            # Assert
            assert len(results) == 3
            assert "Epic: 用户认证系统" in results
            assert "Feature: 用户登录" in results
            assert "Task: 设计登录API" in results
            
            # 验证统计信息
            stats = engine.get_project_statistics()
            assert stats["total_tasks"] == 3
            assert stats["completed_count"] == 3
            
        finally:
            engine.shutdown()
