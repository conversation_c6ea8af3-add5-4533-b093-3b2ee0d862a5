"""
交易日历管理器测试用例

测试TradingCalendarManager的所有功能，包括交易日判断、下一交易日计算、
交易日范围查询等功能。

Author: AQUA Team
Date: 2025-01-26
Version: 1.0
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data_import.trading_calendar_manager import TradingCalendarManager


class TestTradingCalendarManager(unittest.TestCase):
    """交易日历管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.calendar_manager = TradingCalendarManager()
        
        # Mock存储管理器
        self.mock_storage_manager = Mock()
        self.calendar_manager.storage_manager = self.mock_storage_manager
    
    def tearDown(self):
        """测试后清理"""
        self.calendar_manager.clear_cache()
    
    def test_is_trading_day_with_database_data(self):
        """测试交易日判断 - 有数据库数据"""
        # 模拟数据库返回结果
        self.mock_storage_manager.execute_query.return_value = [(True,)]
        
        result = self.calendar_manager.is_trading_day('2024-12-02', 'STOCK', 'SSE')
        
        self.assertTrue(result)
        self.mock_storage_manager.execute_query.assert_called_once()
        
        # 验证SQL查询
        call_args = self.mock_storage_manager.execute_query.call_args
        self.assertIn('SELECT is_trading_day FROM market_trading_calendar', call_args[0][0])
        self.assertEqual(call_args[0][1], ['STOCK', 'SSE', '2024-12-02'])
    
    def test_is_trading_day_fallback_logic(self):
        """测试交易日判断 - 回退逻辑（周末）"""
        # 模拟数据库查询失败
        self.mock_storage_manager.execute_query.side_effect = Exception("Database error")
        
        # 测试周一（应该是交易日）
        result = self.calendar_manager.is_trading_day('2024-12-02', 'STOCK', 'SSE')  # 周一
        self.assertTrue(result)
        
        # 测试周六（应该不是交易日）
        result = self.calendar_manager.is_trading_day('2024-12-07', 'STOCK', 'SSE')  # 周六
        self.assertFalse(result)
        
        # 测试周日（应该不是交易日）
        result = self.calendar_manager.is_trading_day('2024-12-08', 'STOCK', 'SSE')  # 周日
        self.assertFalse(result)
    
    def test_is_trading_day_cache(self):
        """测试交易日判断缓存功能"""
        # 第一次查询
        self.mock_storage_manager.execute_query.return_value = [(True,)]
        result1 = self.calendar_manager.is_trading_day('2024-12-02', 'STOCK', 'SSE')
        
        # 第二次查询（应该使用缓存）
        result2 = self.calendar_manager.is_trading_day('2024-12-02', 'STOCK', 'SSE')
        
        self.assertTrue(result1)
        self.assertTrue(result2)
        # 数据库只应该被查询一次
        self.assertEqual(self.mock_storage_manager.execute_query.call_count, 1)
    
    def test_get_next_trading_date_with_database_data(self):
        """测试获取下一交易日 - 有数据库数据"""
        # 模拟数据库返回结果
        self.mock_storage_manager.execute_query.return_value = [('2024-12-03',)]
        
        result = self.calendar_manager.get_next_trading_date('2024-12-02', 'STOCK', 'SSE')
        
        self.assertEqual(result, '2024-12-03')
        self.mock_storage_manager.execute_query.assert_called_once()
        
        # 验证SQL查询
        call_args = self.mock_storage_manager.execute_query.call_args
        self.assertIn('SELECT MIN(cal_date)', call_args[0][0])
        self.assertEqual(call_args[0][1], ['STOCK', 'SSE', '2024-12-02'])
    
    def test_get_next_trading_date_fallback_logic(self):
        """测试获取下一交易日 - 回退逻辑"""
        # 模拟数据库查询失败
        self.mock_storage_manager.execute_query.side_effect = Exception("Database error")
        
        # 测试从周五到下周一
        result = self.calendar_manager.get_next_trading_date('2024-12-06', 'STOCK', 'SSE')  # 周五
        self.assertEqual(result, '2024-12-09')  # 下周一
        
        # 测试从周一到周二
        result = self.calendar_manager.get_next_trading_date('2024-12-02', 'STOCK', 'SSE')  # 周一
        self.assertEqual(result, '2024-12-03')  # 周二
    
    def test_get_next_trading_date_no_result(self):
        """测试获取下一交易日 - 无结果"""
        # 模拟数据库返回空结果
        self.mock_storage_manager.execute_query.return_value = [(None,)]
        
        result = self.calendar_manager.get_next_trading_date('2024-12-02', 'STOCK', 'SSE')
        
        # 应该使用回退逻辑
        self.assertEqual(result, '2024-12-03')
    
    def test_get_trading_dates_range_with_database_data(self):
        """测试获取交易日范围 - 有数据库数据"""
        # 模拟数据库返回结果
        expected_dates = [('2024-12-02',), ('2024-12-03',), ('2024-12-04',)]
        self.mock_storage_manager.execute_query.return_value = expected_dates
        
        result = self.calendar_manager.get_trading_dates_range(
            '2024-12-02', '2024-12-06', 'STOCK', 'SSE'
        )
        
        expected_result = ['2024-12-02', '2024-12-03', '2024-12-04']
        self.assertEqual(result, expected_result)
        
        # 验证SQL查询
        call_args = self.mock_storage_manager.execute_query.call_args
        self.assertIn('SELECT cal_date FROM market_trading_calendar', call_args[0][0])
        self.assertEqual(call_args[0][1], ['STOCK', 'SSE', '2024-12-02', '2024-12-06'])
    
    def test_get_trading_dates_range_fallback_logic(self):
        """测试获取交易日范围 - 回退逻辑"""
        # 模拟数据库查询失败
        self.mock_storage_manager.execute_query.side_effect = Exception("Database error")
        
        result = self.calendar_manager.get_trading_dates_range(
            '2024-12-02', '2024-12-06', 'STOCK', 'SSE'
        )
        
        # 应该返回周一到周五（排除周末）
        expected_result = ['2024-12-02', '2024-12-03', '2024-12-04', '2024-12-05', '2024-12-06']
        self.assertEqual(result, expected_result)
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        # 设置缓存
        self.calendar_manager._set_cache('test_key', 'test_value')
        
        # 获取缓存
        result = self.calendar_manager._get_cache('test_key')
        self.assertEqual(result, 'test_value')
        
        # 测试缓存统计
        stats = self.calendar_manager.get_cache_stats()
        self.assertEqual(stats['total_cache_entries'], 1)
        self.assertEqual(stats['valid_cache_entries'], 1)
        
        # 清空缓存
        self.calendar_manager.clear_cache()
        stats = self.calendar_manager.get_cache_stats()
        self.assertEqual(stats['total_cache_entries'], 0)
    
    def test_invalid_date_format(self):
        """测试无效日期格式处理"""
        # 模拟数据库查询失败，触发回退逻辑
        self.mock_storage_manager.execute_query.side_effect = Exception("Database error")
        
        # 测试无效日期格式
        result = self.calendar_manager.is_trading_day('invalid-date', 'STOCK', 'SSE')
        self.assertFalse(result)
        
        result = self.calendar_manager.get_next_trading_date('invalid-date', 'STOCK', 'SSE')
        self.assertIsNone(result)
        
        result = self.calendar_manager.get_trading_dates_range(
            'invalid-start', 'invalid-end', 'STOCK', 'SSE'
        )
        self.assertEqual(result, [])
    
    @patch('src.data_import.trading_calendar_manager.datetime')
    def test_cache_expiration(self, mock_datetime):
        """测试缓存过期功能"""
        # 设置当前时间
        base_time = datetime(2024, 12, 1, 10, 0, 0)
        mock_datetime.now.return_value = base_time
        
        # 设置缓存
        self.calendar_manager._set_cache('test_key', 'test_value')
        
        # 模拟时间过去2小时（超过缓存TTL）
        mock_datetime.now.return_value = base_time + timedelta(hours=2)
        
        # 缓存应该过期
        result = self.calendar_manager._get_cache('test_key')
        self.assertIsNone(result)
    
    def test_storage_manager_lazy_initialization(self):
        """测试存储管理器延迟初始化"""
        # 创建新的实例，不设置storage_manager
        new_manager = TradingCalendarManager()
        
        # 模拟导入成功
        with patch('src.data_import.trading_calendar_manager.UnifiedStorageManager') as mock_class:
            mock_instance = Mock()
            mock_class.return_value = mock_instance
            
            # 第一次调用应该初始化存储管理器
            storage_manager = new_manager._get_storage_manager()
            
            self.assertEqual(storage_manager, mock_instance)
            mock_class.assert_called_once()
    
    def test_storage_manager_import_error(self):
        """测试存储管理器导入错误处理"""
        new_manager = TradingCalendarManager()
        
        # 模拟导入失败
        with patch('src.data_import.trading_calendar_manager.UnifiedStorageManager', side_effect=ImportError("Module not found")):
            with self.assertRaises(ImportError):
                new_manager._get_storage_manager()


class TestTradingCalendarManagerIntegration(unittest.TestCase):
    """交易日历管理器集成测试类"""
    
    def setUp(self):
        """集成测试前准备"""
        self.calendar_manager = TradingCalendarManager()
    
    def test_real_date_calculations(self):
        """测试真实日期计算（不依赖数据库）"""
        # 这些测试使用回退逻辑，不需要数据库
        
        # 测试已知的交易日（周一到周五）
        test_cases = [
            ('2024-12-02', True),   # 周一
            ('2024-12-03', True),   # 周二
            ('2024-12-04', True),   # 周三
            ('2024-12-05', True),   # 周四
            ('2024-12-06', True),   # 周五
            ('2024-12-07', False),  # 周六
            ('2024-12-08', False),  # 周日
        ]
        
        # 模拟数据库查询失败，强制使用回退逻辑
        with patch.object(self.calendar_manager, '_get_storage_manager', side_effect=Exception("No database")):
            for date, expected in test_cases:
                result = self.calendar_manager.is_trading_day(date, 'STOCK', 'SSE')
                self.assertEqual(result, expected, f"Date {date} should be {'trading' if expected else 'non-trading'} day")
    
    def test_next_trading_date_sequence(self):
        """测试下一交易日序列计算"""
        # 模拟数据库查询失败，使用回退逻辑
        with patch.object(self.calendar_manager, '_get_storage_manager', side_effect=Exception("No database")):
            # 从周五到下周一
            result = self.calendar_manager.get_next_trading_date('2024-12-06', 'STOCK', 'SSE')
            self.assertEqual(result, '2024-12-09')
            
            # 从周四到周五
            result = self.calendar_manager.get_next_trading_date('2024-12-05', 'STOCK', 'SSE')
            self.assertEqual(result, '2024-12-06')
    
    def test_trading_dates_range_week(self):
        """测试一周交易日范围"""
        # 模拟数据库查询失败，使用回退逻辑
        with patch.object(self.calendar_manager, '_get_storage_manager', side_effect=Exception("No database")):
            result = self.calendar_manager.get_trading_dates_range(
                '2024-12-02', '2024-12-08', 'STOCK', 'SSE'
            )
            
            # 应该返回周一到周五
            expected = ['2024-12-02', '2024-12-03', '2024-12-04', '2024-12-05', '2024-12-06']
            self.assertEqual(result, expected)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
