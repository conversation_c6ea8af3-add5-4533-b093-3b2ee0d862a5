"""
TUSHARE数据源增量采集集成测试

使用真实TUSHARE数据验证增量采集功能的完整性和正确性。
测试包括期货数据、股票数据的增量采集，以及数据完整性验证。

Author: AQUA Team
Date: 2025-01-26
Version: 1.0
"""

import pytest
import unittest
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.cli.services.collect_service import CollectService
from src.data_import.incremental_collection_helper import IncrementalCollectionHelper
from src.data_import.trading_calendar_manager import TradingCalendarManager


class TestTushareIncrementalCollection(unittest.TestCase):
    """TUSHARE增量采集集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 检查TUSHARE_TOKEN环境变量
        cls.tushare_token = os.getenv('TUSHARE_TOKEN')
        if not cls.tushare_token:
            pytest.skip("未设置TUSHARE_TOKEN环境变量，跳过TUSHARE集成测试")
        
        # 初始化服务
        cls.collect_service = CollectService(environment="test")
        cls.incremental_helper = IncrementalCollectionHelper()
        cls.trading_calendar = TradingCalendarManager()
        
        # 测试用的标的代码
        cls.test_stock_symbols = ['000001.SZ', '600000.SH']  # 平安银行、浦发银行
        cls.test_futures_symbols = ['RB2501', 'CU2501']     # 螺纹钢、铜
        
        print(f"TUSHARE增量采集测试初始化完成")
        print(f"测试股票: {cls.test_stock_symbols}")
        print(f"测试期货: {cls.test_futures_symbols}")
    
    def setUp(self):
        """每个测试前的准备"""
        self.maxDiff = None
    
    def test_incremental_helper_basic_functionality(self):
        """测试增量采集助手基础功能"""
        print("\n=== 测试增量采集助手基础功能 ===")
        
        # 测试交易所代码映射
        exchange_code = self.incremental_helper._get_exchange_code('000001.SZ', 'stocks')
        self.assertEqual(exchange_code, 'SZSE')
        print(f"✅ 股票交易所映射: 000001.SZ -> {exchange_code}")
        
        exchange_code = self.incremental_helper._get_exchange_code('RB2501', 'futures')
        self.assertEqual(exchange_code, 'SHFE')
        print(f"✅ 期货交易所映射: RB2501 -> {exchange_code}")
        
        # 测试默认配置获取
        default_range = self.incremental_helper._get_default_range_from_settings('stocks', 'daily')
        self.assertIn('start_date', default_range)
        self.assertIn('end_date', default_range)
        print(f"✅ 默认配置获取: {default_range}")
    
    def test_trading_calendar_functionality(self):
        """测试交易日历功能"""
        print("\n=== 测试交易日历功能 ===")
        
        # 测试已知的交易日（使用回退逻辑）
        test_date = '2024-12-02'  # 周一
        is_trading = self.trading_calendar.is_trading_day(test_date, 'STOCK', 'SSE')
        print(f"✅ 交易日判断: {test_date} -> {is_trading}")
        
        # 测试下一交易日计算
        next_date = self.trading_calendar.get_next_trading_date('2024-12-06', 'STOCK', 'SSE')  # 周五
        print(f"✅ 下一交易日: 2024-12-06 -> {next_date}")
        self.assertIsNotNone(next_date)
        
        # 测试交易日范围
        trading_dates = self.trading_calendar.get_trading_dates_range(
            '2024-12-02', '2024-12-06', 'STOCK', 'SSE'
        )
        print(f"✅ 交易日范围: {len(trading_dates)}天 -> {trading_dates}")
        self.assertGreater(len(trading_dates), 0)
    
    @pytest.mark.skipif(not os.getenv('TUSHARE_TOKEN'), reason="需要TUSHARE_TOKEN")
    def test_stock_incremental_collection_first_time(self):
        """测试股票数据首次增量采集（使用默认配置）"""
        print("\n=== 测试股票数据首次增量采集 ===")
        
        try:
            # 使用小范围测试数据（最近3天）
            result = self.collect_service.collect_data(
                symbols=['000001.SZ'],  # 只测试一只股票
                source='tushare',
                data_type='stock',
                freq='daily',
                incremental=True,
                last_days=3  # 限制数据量
            )
            
            print(f"采集结果: {result}")
            
            # 验证结果结构
            self.assertIn('success', result)
            self.assertIn('collected_symbols', result)
            self.assertIn('total_rows', result)
            
            if result['success']:
                print(f"✅ 股票增量采集成功")
                print(f"   采集标的: {result['collected_symbols']}")
                print(f"   数据行数: {result['total_rows']}")
            else:
                print(f"⚠️  股票增量采集失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 股票增量采集测试异常: {e}")
            # 不让测试失败，因为可能是网络或API限制问题
            self.skipTest(f"TUSHARE API调用失败: {e}")
    
    @pytest.mark.skipif(not os.getenv('TUSHARE_TOKEN'), reason="需要TUSHARE_TOKEN")
    def test_futures_incremental_collection_first_time(self):
        """测试期货数据首次增量采集（使用默认配置）"""
        print("\n=== 测试期货数据首次增量采集 ===")
        
        try:
            # 使用小范围测试数据（最近3天）
            result = self.collect_service.collect_data(
                symbols=['RB2501'],  # 只测试一个期货合约
                source='tushare',
                data_type='futures',
                freq='daily',
                incremental=True,
                last_days=3  # 限制数据量
            )
            
            print(f"采集结果: {result}")
            
            # 验证结果结构
            self.assertIn('success', result)
            self.assertIn('collected_symbols', result)
            self.assertIn('total_rows', result)
            
            if result['success']:
                print(f"✅ 期货增量采集成功")
                print(f"   采集标的: {result['collected_symbols']}")
                print(f"   数据行数: {result['total_rows']}")
            else:
                print(f"⚠️  期货增量采集失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 期货增量采集测试异常: {e}")
            # 不让测试失败，因为可能是网络或API限制问题
            self.skipTest(f"TUSHARE API调用失败: {e}")
    
    def test_incremental_vs_full_collection_comparison(self):
        """测试增量采集与全量采集的对比"""
        print("\n=== 测试增量采集与全量采集对比 ===")
        
        # 这个测试主要验证逻辑，不实际调用TUSHARE API
        
        # 模拟场景：有历史数据的情况下计算增量范围
        from unittest.mock import Mock, patch
        
        with patch.object(self.incremental_helper, '_get_history_manager') as mock_history:
            with patch.object(self.incremental_helper, '_get_trading_calendar') as mock_calendar:
                
                # 模拟历史记录
                mock_history_manager = Mock()
                mock_history_manager.get_last_import_date.return_value = '2024-12-01'
                mock_history.return_value = mock_history_manager
                
                # 模拟交易日历
                mock_trading_calendar = Mock()
                mock_trading_calendar.get_next_trading_date.return_value = '2024-12-02'
                mock_calendar.return_value = mock_trading_calendar
                
                # 计算增量范围
                incremental_range = self.incremental_helper.calculate_incremental_range(
                    symbols=['000001.SZ'],
                    data_type='stocks',
                    freq='daily',
                    source='TUSHARE'
                )
                
                print(f"✅ 模拟增量范围计算: {incremental_range}")
                
                # 验证结果
                self.assertIsNotNone(incremental_range)
                self.assertEqual(incremental_range['start_date'], '2024-12-02')
                self.assertIn('end_date', incremental_range)
    
    def test_incremental_collection_no_new_data(self):
        """测试无新数据时的增量采集行为"""
        print("\n=== 测试无新数据时的增量采集 ===")
        
        from unittest.mock import Mock, patch
        
        with patch.object(self.incremental_helper, '_get_history_manager') as mock_history:
            with patch.object(self.incremental_helper, '_get_trading_calendar') as mock_calendar:
                
                # 模拟历史记录（最后采集日期是今天）
                mock_history_manager = Mock()
                today = datetime.now().strftime('%Y-%m-%d')
                mock_history_manager.get_last_import_date.return_value = today
                mock_history.return_value = mock_history_manager
                
                # 模拟交易日历（无下一交易日）
                mock_trading_calendar = Mock()
                mock_trading_calendar.get_next_trading_date.return_value = None
                mock_calendar.return_value = mock_trading_calendar
                
                # 计算增量范围
                incremental_range = self.incremental_helper.calculate_incremental_range(
                    symbols=['000001.SZ'],
                    data_type='stocks',
                    freq='daily',
                    source='TUSHARE'
                )
                
                print(f"✅ 无新数据场景: {incremental_range}")
                
                # 验证结果
                self.assertIsNone(incremental_range)
    
    def test_incremental_collection_fallback_mechanism(self):
        """测试增量采集失败时的回退机制"""
        print("\n=== 测试增量采集回退机制 ===")
        
        from unittest.mock import Mock, patch
        
        # 模拟增量采集助手初始化失败
        with patch.object(self.collect_service, '_get_incremental_helper', side_effect=Exception("Helper failed")):
            
            # 尝试增量采集
            result = self.collect_service._handle_incremental_collection(
                symbols=['000001.SZ'],
                source='tushare',
                data_type='stocks',
                freq='daily',
                preview=False
            )
            
            print(f"✅ 回退机制测试: {result}")
            
            # 验证回退行为（应该返回None让原有逻辑继续）
            self.assertIsNone(result)
    
    def test_incremental_collection_parameter_validation(self):
        """测试增量采集参数验证"""
        print("\n=== 测试增量采集参数验证 ===")
        
        # 测试should_use_incremental方法
        
        # 场景1：用户指定了时间范围，不建议增量采集
        should_use = self.incremental_helper.should_use_incremental(
            symbols=['000001.SZ'],
            data_type='stocks',
            start_date='2024-11-01',
            end_date='2024-12-01'
        )
        self.assertFalse(should_use)
        print(f"✅ 用户指定时间范围: should_use_incremental = {should_use}")
        
        # 场景2：无历史记录，不建议增量采集
        from unittest.mock import Mock, patch
        
        with patch.object(self.incremental_helper, '_get_history_manager') as mock_history:
            mock_history_manager = Mock()
            mock_history_manager.get_last_import_date.return_value = None
            mock_history.return_value = mock_history_manager
            
            should_use = self.incremental_helper.should_use_incremental(
                symbols=['000001.SZ'],
                data_type='stocks'
            )
            self.assertFalse(should_use)
            print(f"✅ 无历史记录: should_use_incremental = {should_use}")
    
    def test_exchange_code_mapping_comprehensive(self):
        """测试交易所代码映射的全面性"""
        print("\n=== 测试交易所代码映射 ===")
        
        # 测试股票交易所映射
        stock_test_cases = [
            ('000001.SZ', 'SZSE'),
            ('600000.SH', 'SSE'),
            ('000001', 'SSE'),  # 默认情况
        ]
        
        for symbol, expected in stock_test_cases:
            result = self.incremental_helper._get_exchange_code(symbol, 'stocks')
            self.assertEqual(result, expected)
            print(f"✅ 股票映射: {symbol} -> {result}")
        
        # 测试期货交易所映射
        futures_test_cases = [
            ('RB2501', 'SHFE'),   # 上期所
            ('IF2501', 'CFFEX'),  # 中金所
            ('A2501', 'DCE'),     # 大商所
            ('CF2501', 'CZCE'),   # 郑商所
        ]
        
        for symbol, expected in futures_test_cases:
            result = self.incremental_helper._get_exchange_code(symbol, 'futures')
            self.assertEqual(result, expected)
            print(f"✅ 期货映射: {symbol} -> {result}")


class TestTushareIncrementalPerformance(unittest.TestCase):
    """TUSHARE增量采集性能测试类"""
    
    def setUp(self):
        """性能测试前准备"""
        self.incremental_helper = IncrementalCollectionHelper()
    
    def test_incremental_range_calculation_performance(self):
        """测试增量范围计算性能"""
        print("\n=== 测试增量范围计算性能 ===")
        
        import time
        from unittest.mock import Mock, patch
        
        # 模拟大量标的的性能测试
        symbols = [f"00000{i}.SZ" for i in range(1, 101)]  # 100个标的
        
        with patch.object(self.incremental_helper, '_get_history_manager') as mock_history:
            with patch.object(self.incremental_helper, '_get_trading_calendar') as mock_calendar:
                
                # 模拟快速响应
                mock_history_manager = Mock()
                mock_history_manager.get_last_import_date.return_value = '2024-12-01'
                mock_history.return_value = mock_history_manager
                
                mock_trading_calendar = Mock()
                mock_trading_calendar.get_next_trading_date.return_value = '2024-12-02'
                mock_calendar.return_value = mock_trading_calendar
                
                # 性能测试
                start_time = time.time()
                
                result = self.incremental_helper.calculate_incremental_range(
                    symbols=symbols,
                    data_type='stocks',
                    freq='daily',
                    source='TUSHARE'
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"✅ 100个标的增量范围计算耗时: {duration:.3f}秒")
                print(f"✅ 平均每个标的: {duration/100*1000:.1f}毫秒")
                
                # 性能要求：100个标的应该在1秒内完成
                self.assertLess(duration, 1.0)
                self.assertIsNotNone(result)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
