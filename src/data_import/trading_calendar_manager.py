"""
交易日历管理器

基于market_trading_calendar表提供交易日历查询功能，支持增量采集的时间范围计算。

Author: AQUA Team
Date: 2025-01-26
Version: 1.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class TradingCalendarManager:
    """交易日历管理器 - 基于market_trading_calendar表"""
    
    def __init__(self):
        """初始化交易日历管理器"""
        self.storage_manager = None
        self._cache = {}  # 内存缓存 {cache_key: result}
        self._cache_ttl = 3600  # 缓存1小时
        self._cache_timestamps = {}  # 缓存时间戳
        self._initialized = False
        
    def _get_storage_manager(self):
        """延迟初始化存储管理器"""
        if self.storage_manager is None:
            try:
                from src.database.unified_storage_manager import UnifiedStorageManager
                self.storage_manager = UnifiedStorageManager()
                logger.info("交易日历管理器：存储管理器初始化成功")
            except ImportError as e:
                logger.error(f"交易日历管理器：无法导入存储管理器 - {e}")
                raise
        return self.storage_manager
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache_timestamps:
            return False
        
        cache_time = self._cache_timestamps[cache_key]
        return (datetime.now() - cache_time).seconds < self._cache_ttl
    
    def _set_cache(self, cache_key: str, value):
        """设置缓存"""
        self._cache[cache_key] = value
        self._cache_timestamps[cache_key] = datetime.now()
    
    def _get_cache(self, cache_key: str):
        """获取缓存"""
        if self._is_cache_valid(cache_key):
            return self._cache.get(cache_key)
        else:
            # 清理过期缓存
            if cache_key in self._cache:
                del self._cache[cache_key]
            if cache_key in self._cache_timestamps:
                del self._cache_timestamps[cache_key]
            return None
    
    def is_trading_day(self, date: str, market_type: str, exchange_code: str) -> bool:
        """
        判断是否为交易日
        
        Args:
            date: 日期字符串，格式：YYYY-MM-DD
            market_type: 市场类型，'STOCK' 或 'FUTURES'
            exchange_code: 交易所代码，如 'SSE', 'SZSE', 'SHFE' 等
            
        Returns:
            bool: True表示是交易日，False表示非交易日
        """
        cache_key = f"is_trading_{date}_{market_type}_{exchange_code}"
        
        # 检查缓存
        cached_result = self._get_cache(cache_key)
        if cached_result is not None:
            return cached_result
        
        try:
            storage_manager = self._get_storage_manager()
            
            query = """
            SELECT is_trading_day FROM market_trading_calendar 
            WHERE market_type = ? AND exchange_code = ? AND cal_date = ?
            """
            
            result = storage_manager.execute_query(
                query, [market_type, exchange_code, date]
            )
            
            is_trading = bool(result[0][0]) if result else False
            
            # 缓存结果
            self._set_cache(cache_key, is_trading)
            
            logger.debug(f"交易日查询: {date} {market_type} {exchange_code} -> {is_trading}")
            return is_trading
            
        except Exception as e:
            logger.warning(f"交易日查询失败，使用简化逻辑: {e}")
            # 回退到简化逻辑：排除周末
            try:
                date_obj = datetime.strptime(date, '%Y-%m-%d')
                is_trading = date_obj.weekday() < 5  # 0-4是周一到周五
                self._set_cache(cache_key, is_trading)
                return is_trading
            except ValueError:
                logger.error(f"无效的日期格式: {date}")
                return False
    
    def get_next_trading_date(self, date: str, market_type: str, 
                            exchange_code: str) -> Optional[str]:
        """
        获取下一个交易日
        
        Args:
            date: 起始日期字符串，格式：YYYY-MM-DD
            market_type: 市场类型，'STOCK' 或 'FUTURES'
            exchange_code: 交易所代码
            
        Returns:
            Optional[str]: 下一个交易日，格式：YYYY-MM-DD，如果没有则返回None
        """
        cache_key = f"next_trading_{date}_{market_type}_{exchange_code}"
        
        # 检查缓存
        cached_result = self._get_cache(cache_key)
        if cached_result is not None:
            return cached_result
        
        try:
            storage_manager = self._get_storage_manager()
            
            query = """
            SELECT MIN(cal_date) as next_trading_date
            FROM market_trading_calendar 
            WHERE market_type = ? 
              AND exchange_code = ?
              AND cal_date > ?
              AND is_trading_day = TRUE
            """
            
            result = storage_manager.execute_query(
                query, [market_type, exchange_code, date]
            )
            
            next_date = result[0][0] if result and result[0][0] else None
            
            # 如果数据库没有数据，使用简化逻辑
            if next_date is None:
                next_date = self._get_next_trading_date_fallback(date)
            
            # 缓存结果
            self._set_cache(cache_key, next_date)
            
            logger.debug(f"下一交易日查询: {date} {market_type} {exchange_code} -> {next_date}")
            return next_date
            
        except Exception as e:
            logger.warning(f"下一交易日查询失败，使用简化逻辑: {e}")
            next_date = self._get_next_trading_date_fallback(date)
            self._set_cache(cache_key, next_date)
            return next_date
    
    def _get_next_trading_date_fallback(self, date: str) -> Optional[str]:
        """
        简化逻辑：获取下一个交易日（排除周末）
        
        Args:
            date: 起始日期字符串
            
        Returns:
            Optional[str]: 下一个交易日
        """
        try:
            current_date = datetime.strptime(date, '%Y-%m-%d')
            
            # 最多查找30天
            for i in range(1, 31):
                next_date = current_date + timedelta(days=i)
                
                # 跳过周末
                if next_date.weekday() < 5:  # 0-4是周一到周五
                    return next_date.strftime('%Y-%m-%d')
            
            logger.warning(f"30天内未找到下一个交易日: {date}")
            return None
            
        except ValueError:
            logger.error(f"无效的日期格式: {date}")
            return None
    
    def get_trading_dates_range(self, start_date: str, end_date: str,
                              market_type: str, exchange_code: str) -> List[str]:
        """
        获取时间范围内的所有交易日
        
        Args:
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            market_type: 市场类型
            exchange_code: 交易所代码
            
        Returns:
            List[str]: 交易日列表，按日期升序排列
        """
        cache_key = f"range_{start_date}_{end_date}_{market_type}_{exchange_code}"
        
        # 检查缓存
        cached_result = self._get_cache(cache_key)
        if cached_result is not None:
            return cached_result
        
        try:
            storage_manager = self._get_storage_manager()
            
            query = """
            SELECT cal_date FROM market_trading_calendar 
            WHERE market_type = ? 
              AND exchange_code = ?
              AND is_trading_day = TRUE 
              AND cal_date BETWEEN ? AND ?
            ORDER BY cal_date
            """
            
            result = storage_manager.execute_query(
                query, [market_type, exchange_code, start_date, end_date]
            )
            
            trading_dates = [row[0] for row in result] if result else []
            
            # 如果数据库没有数据，使用简化逻辑
            if not trading_dates:
                trading_dates = self._get_trading_dates_range_fallback(start_date, end_date)
            
            # 缓存结果
            self._set_cache(cache_key, trading_dates)
            
            logger.debug(f"交易日范围查询: {start_date} to {end_date} -> {len(trading_dates)}天")
            return trading_dates
            
        except Exception as e:
            logger.warning(f"交易日范围查询失败，使用简化逻辑: {e}")
            trading_dates = self._get_trading_dates_range_fallback(start_date, end_date)
            self._set_cache(cache_key, trading_dates)
            return trading_dates
    
    def _get_trading_dates_range_fallback(self, start_date: str, end_date: str) -> List[str]:
        """
        简化逻辑：获取时间范围内的交易日（排除周末）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[str]: 交易日列表
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            
            trading_dates = []
            current = start
            
            while current <= end:
                # 排除周末
                if current.weekday() < 5:  # 0-4是周一到周五
                    trading_dates.append(current.strftime('%Y-%m-%d'))
                current += timedelta(days=1)
            
            return trading_dates
            
        except ValueError as e:
            logger.error(f"无效的日期格式: {start_date} 或 {end_date} - {e}")
            return []
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._cache_timestamps.clear()
        logger.info("交易日历缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        valid_cache_count = sum(
            1 for key in self._cache.keys()
            if self._is_cache_valid(key)
        )

        return {
            'total_cache_entries': len(self._cache),
            'valid_cache_entries': valid_cache_count,
            'expired_cache_entries': len(self._cache) - valid_cache_count
        }

    def initialize_trading_calendar(self, years: Optional[List[int]] = None) -> Dict[str, int]:
        """
        初始化交易日历数据（从TUSHARE获取）

        Args:
            years: 要初始化的年份列表，默认为当前年份和下一年份

        Returns:
            Dict[str, int]: 初始化结果统计
        """
        if years is None:
            current_year = datetime.now().year
            years = [current_year, current_year + 1]

        logger.info(f"开始初始化交易日历数据，年份: {years}")

        total_records = 0
        errors = 0

        try:
            # 检查是否已有数据
            if self._check_calendar_data_exists(years):
                logger.info("交易日历数据已存在，跳过初始化")
                return {'total_records': 0, 'new_records': 0, 'errors': 0, 'status': 'skipped'}

            # 从TUSHARE获取数据
            calendar_data = self._fetch_trading_calendar_from_tushare(years)

            if calendar_data:
                # 插入数据库
                new_records = self._insert_trading_calendar_data(calendar_data)
                total_records = len(calendar_data)

                logger.info(f"交易日历初始化完成: 总记录{total_records}条，新增{new_records}条")

                # 清空缓存，强制重新查询
                self.clear_cache()

                return {
                    'total_records': total_records,
                    'new_records': new_records,
                    'errors': errors,
                    'status': 'success'
                }
            else:
                logger.error("从TUSHARE获取交易日历数据失败")
                return {'total_records': 0, 'new_records': 0, 'errors': 1, 'status': 'failed'}

        except Exception as e:
            logger.error(f"交易日历初始化失败: {e}")
            return {'total_records': 0, 'new_records': 0, 'errors': 1, 'status': 'error'}

    def _check_calendar_data_exists(self, years: List[int]) -> bool:
        """检查交易日历数据是否已存在"""
        try:
            storage_manager = self._get_storage_manager()

            for year in years:
                start_date = f"{year}-01-01"
                end_date = f"{year}-12-31"

                query = """
                SELECT COUNT(*) FROM market_trading_calendar
                WHERE cal_date BETWEEN ? AND ?
                """

                result = storage_manager.execute_query(query, [start_date, end_date])
                count = result[0][0] if result else 0

                if count == 0:
                    return False

            return True

        except Exception as e:
            logger.warning(f"检查交易日历数据失败: {e}")
            return False

    def _fetch_trading_calendar_from_tushare(self, years: List[int]) -> Optional[List[Dict]]:
        """从TUSHARE获取交易日历数据"""
        try:
            import tushare as ts
            import os

            # 获取TUSHARE token
            token = os.getenv('TUSHARE_TOKEN')
            if not token:
                logger.error("未找到TUSHARE_TOKEN环境变量")
                return None

            pro = ts.pro_api(token)
            all_data = []

            for year in years:
                start_date = f"{year}0101"
                end_date = f"{year}1231"

                logger.info(f"获取{year}年交易日历数据...")

                # 获取股票交易日历
                stock_cal = pro.trade_cal(
                    exchange='',
                    start_date=start_date,
                    end_date=end_date
                )

                # 转换数据格式
                for _, row in stock_cal.iterrows():
                    cal_date = datetime.strptime(row['cal_date'], '%Y%m%d').strftime('%Y-%m-%d')

                    # 股票市场（上交所和深交所）
                    for exchange in ['SSE', 'SZSE']:
                        all_data.append({
                            'market_type': 'STOCK',
                            'exchange_code': exchange,
                            'cal_date': cal_date,
                            'is_trading_day': bool(row['is_open']),
                            'data_source': 'TUSHARE',
                            'source_quality': 'HIGH',
                            'source_api': 'trade_cal'
                        })

                # 期货交易日历（主要交易所）
                futures_exchanges = ['SHFE', 'DCE', 'CZCE', 'CFFEX']
                for exchange in futures_exchanges:
                    for _, row in stock_cal.iterrows():
                        cal_date = datetime.strptime(row['cal_date'], '%Y%m%d').strftime('%Y-%m-%d')
                        all_data.append({
                            'market_type': 'FUTURES',
                            'exchange_code': exchange,
                            'cal_date': cal_date,
                            'is_trading_day': bool(row['is_open']),
                            'data_source': 'TUSHARE',
                            'source_quality': 'HIGH',
                            'source_api': 'trade_cal'
                        })

            logger.info(f"从TUSHARE获取交易日历数据完成，共{len(all_data)}条记录")
            return all_data

        except ImportError:
            logger.error("未安装tushare库，无法获取交易日历数据")
            return None
        except Exception as e:
            logger.error(f"从TUSHARE获取交易日历数据失败: {e}")
            return None

    def _insert_trading_calendar_data(self, calendar_data: List[Dict]) -> int:
        """插入交易日历数据到数据库"""
        try:
            storage_manager = self._get_storage_manager()

            insert_query = """
            INSERT OR REPLACE INTO market_trading_calendar (
                market_type, exchange_code, cal_date, is_trading_day,
                data_source, source_quality, source_api
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            new_records = 0
            batch_size = 1000

            for i in range(0, len(calendar_data), batch_size):
                batch = calendar_data[i:i + batch_size]

                batch_data = [
                    (
                        record['market_type'],
                        record['exchange_code'],
                        record['cal_date'],
                        record['is_trading_day'],
                        record['data_source'],
                        record['source_quality'],
                        record['source_api']
                    )
                    for record in batch
                ]

                result = storage_manager.execute_batch(insert_query, batch_data)
                new_records += len(batch)

                logger.debug(f"插入交易日历数据批次: {len(batch)}条")

            logger.info(f"交易日历数据插入完成，共{new_records}条记录")
            return new_records

        except Exception as e:
            logger.error(f"插入交易日历数据失败: {e}")
            raise
