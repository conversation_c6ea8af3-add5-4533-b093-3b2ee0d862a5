#!/usr/bin/env python3
"""
DataProcessor - 数据守门员
统一的、规则驱动的、跨平台的清洗与去重核心

特性：
1. 基于混合规则引擎的数据清洗
2. 数据源优先级管理的去重策略
3. 多级错误处理机制
4. 混合处理引擎（Polars + DuckDB）
5. 实时性能监控和质量评估

架构位置：Extractor → DataProcessor → UnifiedStorageManager → Database
"""

import polars as pl
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass
import threading
import time

# 复用现有组件
from .processors.hybrid_rule_engine import HybridRuleEngine, ValidationRule, FieldRule, ErrorLevel, ErrorAction
from .processors.data_quality_controller import DataQualityController
from .processors.data_validator import DataValidator
from .mappers.business_table_mapper import BusinessTableMapper
from ..database.connection_manager import DuckDBConnectionManager
from ..utils.logger import get_logger
from ..utils.time_utils import get_beijing_time_now


@dataclass
class ProcessingResult:
    """数据处理结果"""
    clean_data: pl.DataFrame
    dirty_data: pl.DataFrame
    duplicate_data: pl.DataFrame
    processing_stats: Dict[str, Any]
    quality_score: float
    error_details: List[Dict[str, Any]]


@dataclass
class DataSourceInfo:
    """数据源信息"""
    source_name: str
    priority: int
    quality: str
    trust_score: float
    record_count: int


class ProcessingEngine:
    """处理引擎选择器"""
    POLARS = "polars"
    DUCKDB = "duckdb"


class DataProcessor:
    """
    DataProcessor主类 - 数据守门员
    
    为AQUA项目的数据采集流程建立统一的清洗与去重核心
    """

    def __init__(self, environment: str = "dev", db_manager: Optional[DuckDBConnectionManager] = None, config_loader=None):
        """
        初始化DataProcessor
        
        Args:
            environment: 运行环境
            db_manager: 数据库连接管理器
            config_loader: 配置加载器
        """
        self.logger = get_logger(self.__class__.__name__)
        self.environment = environment
        self.db_manager = db_manager
        
        # 加载配置
        if config_loader:
            from ..utils.config_loader import ConfigLoader
            self.config_loader = config_loader
        else:
            from ..utils.config_loader import ConfigLoader
            self.config_loader = ConfigLoader()
        
        # 获取DataProcessor配置
        self.dp_config = self.config_loader.get_data_processor_config(environment)
        
        # 初始化核心组件（复用现有组件）
        try:
            self.rule_engine = HybridRuleEngine(db_manager=db_manager)
            self.logger.info("HybridRuleEngine初始化成功")
        except Exception as e:
            self.logger.warning(f"HybridRuleEngine初始化失败，使用简化模式: {e}")
            self.rule_engine = None
            
        self.quality_controller = DataQualityController()
        self.data_validator = DataValidator()
        self.business_mapper = BusinessTableMapper()
        
        # 获取配置参数
        if self.rule_engine:
            self.global_config = self.rule_engine.get_global_config()
            self.performance_config = self.rule_engine.get_performance_config()
        else:
            # 使用配置文件中的默认配置
            self.global_config = {"data_source_priority": self.dp_config.get("data_source_priority", ["TUSHARE", "FROMC2C", "AKSHARE"])}
            self.performance_config = {
                "small_data_limit": self.dp_config.get("small_data_limit", 100000), 
                "large_data_limit": self.dp_config.get("large_data_limit", 1000000)
            }
        
        # 数据源优先级映射
        self.data_source_priority = self.global_config.get("data_source_priority", self.dp_config.get("data_source_priority", ["TUSHARE", "FROMC2C", "AKSHARE"]))
        
        # 性能阈值
        self.small_data_limit = self.performance_config.get("small_data_limit", self.dp_config.get("small_data_limit", 100000))
        self.large_data_limit = self.performance_config.get("large_data_limit", self.dp_config.get("large_data_limit", 1000000))
        
        # 处理统计
        self.processing_stats = {
            "total_processed": 0,
            "clean_records": 0,
            "dirty_records": 0,
            "duplicate_records": 0,
            "processing_time": 0.0,
            "avg_quality_score": 0.0,
            "error_rate": 0.0
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        self.logger.info(f"DataProcessor初始化完成 (环境: {environment})")

    def process(self, data: Union[pl.DataFrame, pd.DataFrame], table_name: str, 
                data_source: str = "UNKNOWN") -> ProcessingResult:
        """
        数据处理主入口
        
        Args:
            data: 输入数据 (支持Polars或Pandas)
            table_name: 目标表名
            data_source: 数据源标识
            
        Returns:
            ProcessingResult: 处理结果
        """
        start_time = time.time()
        
        self.logger.info(f"开始处理数据: {table_name}, 数据源: {data_source}, 记录数: {len(data)}")
        
        try:
            # 1. 数据格式转换和预处理
            pl_data = self._prepare_data(data)
            
            # 2. 选择处理引擎
            engine = self._select_processing_engine(len(pl_data))
            self.logger.debug(f"选择处理引擎: {engine}")
            
            # 3. 数据清洗
            clean_data, dirty_data = self._clean_data(pl_data, table_name)
            
            # 4. 数据去重
            unique_data, duplicate_data = self._deduplicate_data(clean_data, table_name, data_source)
            
            # 5. 质量评估
            quality_score = self._calculate_quality_score(pl_data, unique_data, dirty_data)
            
            # 6. 生成处理统计
            processing_time = time.time() - start_time
            stats = self._generate_processing_stats(pl_data, unique_data, dirty_data, duplicate_data, processing_time)
            
            # 7. 错误详情收集
            error_details = self._collect_error_details(dirty_data, duplicate_data)
            
            # 更新全局统计
            self._update_global_stats(stats, quality_score)
            
            result = ProcessingResult(
                clean_data=unique_data,
                dirty_data=dirty_data,
                duplicate_data=duplicate_data,
                processing_stats=stats,
                quality_score=quality_score,
                error_details=error_details
            )
            
            self.logger.info(f"数据处理完成: 清洁记录 {len(unique_data)}, 脏数据 {len(dirty_data)}, "
                           f"重复数据 {len(duplicate_data)}, 质量评分 {quality_score:.2f}, "
                           f"处理时间 {processing_time:.2f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            raise

    def _prepare_data(self, data: Union[pl.DataFrame, pd.DataFrame]) -> pl.DataFrame:
        """
        数据预处理和格式转换
        
        Args:
            data: 输入数据
            
        Returns:
            pl.DataFrame: Polars格式数据
        """
        if isinstance(data, pd.DataFrame):
            # Pandas转Polars
            return pl.from_pandas(data)
        elif isinstance(data, pl.DataFrame):
            return data
        else:
            raise ValueError(f"不支持的数据格式: {type(data)}")

    def _select_processing_engine(self, record_count: int) -> str:
        """
        选择处理引擎 (D3: 混合模式)
        
        Args:
            record_count: 记录数量
            
        Returns:
            str: 处理引擎类型
        """
        if record_count < self.small_data_limit:
            return ProcessingEngine.POLARS
        else:
            return ProcessingEngine.DUCKDB

    def _clean_data(self, data: pl.DataFrame, table_name: str) -> Tuple[pl.DataFrame, pl.DataFrame]:
        """
        数据清洗 (C3: 多级错误处理)
        
        Args:
            data: 输入数据
            table_name: 表名
            
        Returns:
            Tuple[pl.DataFrame, pl.DataFrame]: (清洁数据, 脏数据)
        """
        self.logger.debug(f"开始数据清洗: {table_name}")
        
        # 获取表规则
        if self.rule_engine:
            validation_rules = self.rule_engine.get_validation_rules(table_name)
            required_fields = self.rule_engine.get_required_fields(table_name)
        else:
            # 简化模式：使用基本验证规则
            validation_rules = []
            required_fields = []
        
        # 初始化结果
        clean_data = data.clone()
        
        # 1. 必需字段检查
        missing_required = []
        for field in required_fields:
            if field not in clean_data.columns:
                missing_required.append(field)
        
        if missing_required:
            self.logger.error(f"缺少必需字段: {missing_required}")
            # 所有数据都标记为脏数据
            dirty_data = clean_data.with_columns([
                pl.lit("missing_required_field").alias("error_type"),
                pl.lit(f"Missing fields: {', '.join(missing_required)}").alias("error_reason")
            ])
            return pl.DataFrame(), dirty_data
        
        # 初始化脏数据收集器
        dirty_records = []
        
        # 2. 字段级验证
        clean_data = self._validate_fields(clean_data, table_name)
        
        # 3. 跨字段验证（简化模式或规则引擎模式补充）
        clean_data, cross_field_dirty_data = self._apply_cross_field_validation(clean_data)
        if len(cross_field_dirty_data) > 0:
            dirty_records.append(cross_field_dirty_data)
        
        # 4. 跨字段验证规则
        for rule in validation_rules:
            if rule.rule_type == "cross_field":
                clean_data, rule_dirty_data = self._apply_validation_rule(clean_data, rule)
                if len(rule_dirty_data) > 0:
                    dirty_records.append(rule_dirty_data)
        
        # 5. 合并脏数据
        if dirty_records:
            dirty_data = pl.concat(dirty_records)
        else:
            dirty_data = pl.DataFrame()
        
        self.logger.debug(f"数据清洗完成: 清洁 {len(clean_data)}, 脏数据 {len(dirty_data)}")
        
        return clean_data, dirty_data

    def _validate_fields(self, data: pl.DataFrame, table_name: str) -> pl.DataFrame:
        """
        字段级验证
        
        Args:
            data: 输入数据
            table_name: 表名
            
        Returns:
            pl.DataFrame: 验证后的数据
        """
        validated_data = data.clone()
        
        for column in data.columns:
            if self.rule_engine:
                field_rule = self.rule_engine.get_field_rules(table_name, column)
                if field_rule:
                    validated_data = self._apply_field_rule(validated_data, column, field_rule)
            else:
                # 简化模式：基本数据验证
                validated_data = self._apply_basic_field_validation(validated_data, column)
        
        return validated_data

    def _apply_field_rule(self, data: pl.DataFrame, column: str, field_rule: FieldRule) -> pl.DataFrame:
        """
        应用字段规则
        
        Args:
            data: 数据
            column: 字段名
            field_rule: 字段规则
            
        Returns:
            pl.DataFrame: 处理后的数据
        """
        if column not in data.columns:
            return data
            
        result_data = data.clone()
        
        # 非负数检查
        if "non_negative" in field_rule.constraints:
            if data[column].dtype in [pl.Int64, pl.Int32, pl.Float64, pl.Float32]:
                result_data = result_data.filter(pl.col(column) >= 0)
        
        # 范围检查
        if field_rule.range_min is not None and field_rule.range_max is not None:
            if data[column].dtype in [pl.Int64, pl.Int32, pl.Float64, pl.Float32]:
                result_data = result_data.filter(
                    (pl.col(column) >= field_rule.range_min) & 
                    (pl.col(column) <= field_rule.range_max)
                )
        
        # 非空检查
        if "not_empty" in field_rule.constraints:
            if data[column].dtype == pl.Utf8:
                result_data = result_data.filter((pl.col(column).is_not_null()) & (pl.col(column) != ""))
        
        # 格式检查
        if field_rule.format_pattern and data[column].dtype == pl.Utf8:
            result_data = result_data.filter(pl.col(column).str.contains(field_rule.format_pattern))
        
        # 有效值检查
        if field_rule.valid_values:
            result_data = result_data.filter(pl.col(column).is_in(field_rule.valid_values))
        
        return result_data

    def _apply_basic_field_validation(self, data: pl.DataFrame, column: str) -> pl.DataFrame:
        """
        简化模式：基本字段验证
        
        Args:
            data: 数据
            column: 字段名
            
        Returns:
            pl.DataFrame: 验证后的数据
        """
        if column not in data.columns:
            return data
            
        result_data = data.clone()
        
        # 基本的数值字段验证
        if data[column].dtype in [pl.Int64, pl.Int32, pl.Float64, pl.Float32]:
            # 过滤掉负数价格字段（对于price相关字段）
            if "price" in column.lower():
                result_data = result_data.filter(pl.col(column) >= 0)
        
        return result_data
    
    def _apply_cross_field_validation(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, pl.DataFrame]:
        """
        简化模式：跨字段验证
        
        Args:
            data: 数据
            
        Returns:
            Tuple[pl.DataFrame, pl.DataFrame]: (验证通过的数据, 验证失败的数据)
        """
        if not all(col in data.columns for col in ["open_price", "high_price", "low_price", "close_price"]):
            return data, pl.DataFrame()
        
        # 构建综合验证条件
        valid_mask = (
            # 高价 >= 低价
            (pl.col("high_price") >= pl.col("low_price")) &
            # 开盘价在高低价之间  
            (pl.col("open_price") >= pl.col("low_price")) & 
            (pl.col("open_price") <= pl.col("high_price")) &
            # 收盘价在高低价之间
            (pl.col("close_price") >= pl.col("low_price")) & 
            (pl.col("close_price") <= pl.col("high_price"))
        )
        
        # 分离清洁和脏数据
        clean_data = data.filter(valid_mask)
        dirty_data = data.filter(~valid_mask).with_columns([
            pl.lit("cross_field_validation").alias("error_type"),
            pl.lit("OHLC price inconsistency").alias("error_reason")
        ])
        
        return clean_data, dirty_data

    def _get_default_primary_key(self, data: pl.DataFrame, table_name: str) -> List[str]:
        """
        简化模式：获取默认主键字段
        
        Args:
            data: 数据
            table_name: 表名
            
        Returns:
            List[str]: 主键字段列表
        """
        # 根据常见字段模式确定主键
        potential_keys = []
        
        # 股票数据常见主键
        if "ts_code" in data.columns and "trade_date" in data.columns:
            potential_keys = ["ts_code", "trade_date"]
        # 期货数据常见主键    
        elif "contract_code" in data.columns and "trade_datetime" in data.columns:
            potential_keys = ["contract_code", "trade_datetime"]
            if "frequency" in data.columns:
                potential_keys.append("frequency")
        # 通用symbol+date模式
        elif "symbol" in data.columns:
            potential_keys = ["symbol"]
            if "date" in data.columns:
                potential_keys.append("date")
            elif "datetime" in data.columns:
                potential_keys.append("datetime")
        
        # 验证字段是否真实存在
        valid_keys = [key for key in potential_keys if key in data.columns]
        
        if valid_keys:
            self.logger.debug(f"简化模式识别主键: {valid_keys}")
            return valid_keys
        else:
            self.logger.warning(f"无法识别表 {table_name} 的主键字段")
            return []

    def _apply_validation_rule(self, data: pl.DataFrame, rule: ValidationRule) -> Tuple[pl.DataFrame, pl.DataFrame]:
        """
        应用验证规则
        
        Args:
            data: 输入数据
            rule: 验证规则
            
        Returns:
            Tuple[pl.DataFrame, pl.DataFrame]: (通过验证的数据, 未通过验证的数据)
        """
        try:
            # 解析规则表达式
            if rule.rule_expression == "high_price >= low_price":
                # OHLC一致性检查
                valid_mask = pl.col("high_price") >= pl.col("low_price")
                clean_data = data.filter(valid_mask)
                dirty_data = data.filter(~valid_mask).with_columns([
                    pl.lit(rule.name).alias("error_type"),
                    pl.lit("high_price < low_price").alias("error_reason")
                ])
                
            elif "open_price >= low_price AND open_price <= high_price" in rule.rule_expression:
                # 开盘价范围检查
                valid_mask = (pl.col("open_price") >= pl.col("low_price")) & (pl.col("open_price") <= pl.col("high_price"))
                clean_data = data.filter(valid_mask)
                dirty_data = data.filter(~valid_mask).with_columns([
                    pl.lit(rule.name).alias("error_type"),
                    pl.lit("open_price out of range").alias("error_reason")
                ])
                
            elif "close_price >= low_price AND close_price <= high_price" in rule.rule_expression:
                # 收盘价范围检查
                valid_mask = (pl.col("close_price") >= pl.col("low_price")) & (pl.col("close_price") <= pl.col("high_price"))
                clean_data = data.filter(valid_mask)
                dirty_data = data.filter(~valid_mask).with_columns([
                    pl.lit(rule.name).alias("error_type"),
                    pl.lit("close_price out of range").alias("error_reason")
                ])
                
            else:
                # 对于复杂规则，暂时跳过
                self.logger.warning(f"跳过复杂规则: {rule.rule_expression}")
                return data, pl.DataFrame()
                
            return clean_data, dirty_data
            
        except Exception as e:
            self.logger.error(f"应用验证规则失败: {rule.name}, {e}")
            return data, pl.DataFrame()

    def _deduplicate_data(self, data: pl.DataFrame, table_name: str, data_source: str) -> Tuple[pl.DataFrame, pl.DataFrame]:
        """
        数据去重 (B1: 数据源优先级)
        
        Args:
            data: 清洁数据
            table_name: 表名
            data_source: 数据源
            
        Returns:
            Tuple[pl.DataFrame, pl.DataFrame]: (去重后数据, 重复数据)
        """
        self.logger.debug(f"开始数据去重: {table_name}, 数据源: {data_source}")
        
        # 获取主键字段
        if self.rule_engine:
            primary_key = self.rule_engine.get_primary_key(table_name)
        else:
            # 简化模式：使用常见主键字段
            primary_key = self._get_default_primary_key(data, table_name)
            
        if not primary_key:
            self.logger.warning(f"表 {table_name} 未定义主键，跳过去重")
            return data, pl.DataFrame()
        
        # 检查主键字段是否存在
        missing_pk_fields = [field for field in primary_key if field not in data.columns]
        if missing_pk_fields:
            self.logger.error(f"缺少主键字段: {missing_pk_fields}")
            return data, pl.DataFrame()
        
        # 1. 内部去重（数据集内部重复）
        unique_data = data.unique(subset=primary_key, keep="first")
        
        # 正确计算内部重复记录
        if len(data) > len(unique_data):
            # 使用is_duplicated()找出重复记录
            is_duplicate_mask = data.select(primary_key).is_duplicated()
            internal_duplicates = data.filter(is_duplicate_mask)
        else:
            internal_duplicates = pl.DataFrame()
        
        # 2. 与数据库已有数据去重
        if self.db_manager and len(unique_data) > 0:
            existing_data = self._get_existing_data(table_name, primary_key, unique_data)
            if len(existing_data) > 0:
                # 使用anti join找出新数据
                unique_data = unique_data.join(existing_data, on=primary_key, how="anti")
        
        duplicate_count = len(data) - len(unique_data)
        
        self.logger.debug(f"去重完成: 唯一数据 {len(unique_data)}, 重复数据 {duplicate_count}")
        
        return unique_data, internal_duplicates

    def _get_existing_data(self, table_name: str, primary_key: List[str], data: pl.DataFrame) -> pl.DataFrame:
        """
        获取数据库中已存在的数据
        
        Args:
            table_name: 表名
            primary_key: 主键字段
            data: 待检查数据
            
        Returns:
            pl.DataFrame: 已存在的数据
        """
        try:
            # 构建查询SQL
            pk_columns = ", ".join(primary_key)
            query = f"SELECT {pk_columns} FROM {table_name}"
            
            with self.db_manager.get_connection() as conn:
                result = conn.execute(query).fetchall()
                
            if result:
                # 转换为Polars DataFrame
                column_names = primary_key
                existing_data = pl.DataFrame(result, schema=column_names)
                return existing_data
            else:
                return pl.DataFrame()
                
        except Exception as e:
            self.logger.warning(f"查询已存在数据失败: {e}")
            return pl.DataFrame()

    def _calculate_quality_score(self, original_data: pl.DataFrame, clean_data: pl.DataFrame, 
                                dirty_data: pl.DataFrame) -> float:
        """
        计算数据质量评分
        
        Args:
            original_data: 原始数据
            clean_data: 清洁数据
            dirty_data: 脏数据
            
        Returns:
            float: 质量评分 (0-1)
        """
        if len(original_data) == 0:
            return 0.0
            
        clean_ratio = len(clean_data) / len(original_data)
        return clean_ratio

    def _generate_processing_stats(self, original_data: pl.DataFrame, clean_data: pl.DataFrame, 
                                 dirty_data: pl.DataFrame, duplicate_data: pl.DataFrame, 
                                 processing_time: float) -> Dict[str, Any]:
        """
        生成处理统计信息
        
        Args:
            original_data: 原始数据
            clean_data: 清洁数据
            dirty_data: 脏数据
            duplicate_data: 重复数据
            processing_time: 处理时间
            
        Returns:
            Dict: 统计信息
        """
        total_records = len(original_data)
        clean_records = len(clean_data)
        dirty_records = len(dirty_data)
        duplicate_records = len(duplicate_data)
        
        return {
            "total_records": total_records,
            "clean_records": clean_records,
            "dirty_records": dirty_records,
            "duplicate_records": duplicate_records,
            "clean_ratio": clean_records / total_records if total_records > 0 else 0.0,
            "dirty_ratio": dirty_records / total_records if total_records > 0 else 0.0,
            "duplicate_ratio": duplicate_records / total_records if total_records > 0 else 0.0,
            "processing_time": processing_time,
            "processing_speed": total_records / processing_time if processing_time > 0 else 0.0
        }

    def _collect_error_details(self, dirty_data: pl.DataFrame, duplicate_data: pl.DataFrame) -> List[Dict[str, Any]]:
        """
        收集错误详情
        
        Args:
            dirty_data: 脏数据
            duplicate_data: 重复数据
            
        Returns:
            List[Dict]: 错误详情列表
        """
        error_details = []
        
        # 脏数据错误
        if len(dirty_data) > 0 and "error_type" in dirty_data.columns:
            try:
                # 使用Polars原生方法避免pyarrow依赖
                error_counts = dirty_data.group_by("error_type").count()
                for row in error_counts.iter_rows(named=True):
                    error_details.append({
                        "error_type": row["error_type"],
                        "error_count": row["count"],
                        "category": "data_quality"
                    })
            except Exception as e:
                # 回退方案：简单计数
                self.logger.warning(f"错误详情收集失败，使用简化统计: {e}")
                error_details.append({
                    "error_type": "data_quality_issues",
                    "error_count": len(dirty_data),
                    "category": "data_quality"
                })
        
        # 重复数据
        if len(duplicate_data) > 0:
            error_details.append({
                "error_type": "duplicate_records",
                "error_count": len(duplicate_data),
                "category": "duplication"
            })
        
        return error_details

    def _update_global_stats(self, stats: Dict[str, Any], quality_score: float):
        """
        更新全局统计
        
        Args:
            stats: 处理统计
            quality_score: 质量评分
        """
        with self._lock:
            self.processing_stats["total_processed"] += stats["total_records"]
            self.processing_stats["clean_records"] += stats["clean_records"]
            self.processing_stats["dirty_records"] += stats["dirty_records"]
            self.processing_stats["duplicate_records"] += stats["duplicate_records"]
            self.processing_stats["processing_time"] += stats["processing_time"]
            
            # 计算平均质量评分
            total_batches = self.processing_stats.get("batch_count", 0) + 1
            current_avg = self.processing_stats["avg_quality_score"]
            self.processing_stats["avg_quality_score"] = (current_avg * (total_batches - 1) + quality_score) / total_batches
            self.processing_stats["batch_count"] = total_batches
            
            # 计算错误率
            total_records = self.processing_stats["total_processed"]
            if total_records > 0:
                self.processing_stats["error_rate"] = self.processing_stats["dirty_records"] / total_records

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            Dict: 统计信息
        """
        with self._lock:
            return self.processing_stats.copy()

    def get_rule_engine_stats(self) -> Dict[str, Any]:
        """
        获取规则引擎统计信息
        
        Returns:
            Dict: 规则引擎统计
        """
        return self.rule_engine.get_stats()

    def add_dynamic_rule(self, table_name: str, rule: ValidationRule) -> bool:
        """
        添加动态规则
        
        Args:
            table_name: 表名
            rule: 验证规则
            
        Returns:
            bool: 是否添加成功
        """
        return self.rule_engine.add_dynamic_rule(table_name, rule)

    def remove_dynamic_rule(self, table_name: str, rule_name: str) -> bool:
        """
        删除动态规则
        
        Args:
            table_name: 表名
            rule_name: 规则名
            
        Returns:
            bool: 是否删除成功
        """
        return self.rule_engine.remove_dynamic_rule(table_name, rule_name)

    def close(self):
        """关闭DataProcessor"""
        self.rule_engine.close()
        self.logger.info("DataProcessor已关闭")